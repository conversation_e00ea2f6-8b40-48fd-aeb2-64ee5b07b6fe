# Testing IndexedDB Migration

## How to Test the Migration

1. **Start the Application**
   ```bash
   npm start
   ```

2. **Open Browser DevTools**
   - Press F12 or right-click → Inspect
   - Go to the "Application" tab
   - Look for "IndexedDB" in the left sidebar

3. **Test Basic Functionality**
   - Click "Start Fresh" to begin a quiz
   - Answer a few questions (both correct and incorrect)
   - Check that questions load properly
   - Verify that progress is being saved

4. **Test Session Persistence**
   - Start a quiz and answer a few questions
   - Refresh the page (F5)
   - You should see a "Resume Session" button
   - Click "Resume Session" to continue where you left off

5. **Test IndexedDB Storage**
   - In DevTools → Application → IndexedDB
   - Expand "SnowflakeQuizDB"
   - You should see two object stores:
     - `tracking`: Contains quiz performance data
     - `session`: Contains current session data (when quiz is in progress)

6. **Test Performance Tracking**
   - Complete a quiz with some incorrect answers
   - Start a new quiz in "Review Incorrect Answers" mode
   - Only questions you answered incorrectly should appear

## Expected Behavior

### Before Migration (localStorage)
- Data stored in localStorage under keys:
  - `snowflake-quiz-tracking-v1`
  - `snowflake-quiz-session-v1`
- Limited to ~5-10MB storage
- Synchronous operations

### After Migration (IndexedDB)
- Data stored in IndexedDB database "SnowflakeQuizDB"
- Much larger storage capacity (several GB)
- Asynchronous operations
- Better performance
- Automatic fallback to localStorage if IndexedDB unavailable

## Verification Steps

1. **Check Storage Method**
   - Open browser console
   - Type: `console.log('Using IndexedDB:', window.indexedDB !== undefined)`
   - Should return `true` for modern browsers

2. **Inspect Stored Data**
   - In DevTools → Application → IndexedDB → SnowflakeQuizDB
   - Click on `tracking` store to see performance data
   - Click on `session` store to see current session (if quiz in progress)

3. **Test Fallback**
   - To test localStorage fallback, you would need to disable IndexedDB (advanced)
   - The app should still work with localStorage as backup

## Common Issues and Solutions

### Issue: Quiz doesn't load
**Solution**: Make sure to click "Start Fresh" or "Resume Session" button. The quiz doesn't start automatically.

### Issue: No IndexedDB in DevTools
**Solution**: 
- Make sure you're using a modern browser
- Check that IndexedDB is enabled in browser settings
- The app will fallback to localStorage automatically

### Issue: Session not resuming
**Solution**:
- Check that session data exists in IndexedDB
- Verify that the session hasn't expired or been corrupted
- Try clearing the session and starting fresh

## Performance Comparison

### localStorage (Before)
- Synchronous operations block UI
- JSON serialization/deserialization overhead
- Limited storage space
- Simple key-value storage

### IndexedDB (After)
- Asynchronous operations don't block UI
- Native object storage
- Large storage capacity
- Structured data with indexing capabilities
- Better suited for complex applications

## Migration Success Indicators

✅ Application starts without errors
✅ Quiz loads when "Start Fresh" is clicked
✅ Questions are displayed correctly
✅ Answers can be submitted
✅ Session can be resumed after page refresh
✅ Performance tracking works (review mode)
✅ IndexedDB database is created in DevTools
✅ Data is stored in correct object stores
✅ No console errors related to storage operations
