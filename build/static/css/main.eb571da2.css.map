{"version": 3, "file": "static/css/main.eb571da2.css", "mappings": "AAAA,KAGE,sDAA8D,CAE9D,aAAc,CAHd,qCAA0C,CAD1C,QAAS,CAGT,gBAAiB,CAEjB,yBACF,CAEA,KAGE,eAAgB,CAChB,kBAAmB,CACnB,qDAAoF,CAHpF,kBAAwB,CADxB,eAAgB,CAMhB,eAAgB,CADhB,sBAA4B,CAE5B,wCACF,CAEA,GACE,aAAc,CAEd,gBAAiB,CAGjB,eAAgB,CADhB,oBAAqB,CADrB,kBAAmB,CAFnB,iBAKF,CAEA,OAIE,kBAAmB,CAFnB,WAAY,CACZ,iBAAkB,CAQlB,8BAA2C,CAN3C,UAAW,CAKX,cAAe,CATf,mBAAoB,CAKpB,gBAAiB,CACjB,eAAgB,CAEhB,eAAgB,CADhB,iBAAkB,CAIlB,sDACF,CACA,0BACE,kBAAmB,CACnB,+BAA4C,CAC5C,sCACF,CAGA,uCACE,oBAAqB,CAErB,WAAY,CACZ,iBAAkB,CAElB,2BAA6B,CAD7B,qBAAsB,CAHtB,UAKF,CAEA,aAEE,6DAAqE,CADrE,wBAAiB,CAAjB,gBAEF,CAEA,YACE,4BAA8B,CAC9B,8BAAgC,CAChC,uBACF,CACA,cACE,4BAA8B,CAC9B,8BAAgC,CAChC,oBACF,CACA,kBACE,4BAA8B,CAC9B,8BAAgC,CAChC,uBACF,CAEA,yBACE,KAEE,WAAY,CADZ,qBAEF,CACA,GACE,cACF,CACF,CAEA,yBACE,KAEE,eAAgB,CADhB,oBAEF,CACA,GACE,gBACF,CACF", "sources": ["index.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;\n  background: linear-gradient(120deg, #f4f6fb 60%, #e8f7f7 100%);\n  min-height: 100vh;\n  color: #1b2838;\n  transition: background 0.3s;\n}\n\n.App {\n  max-width: 700px;\n  margin: 40px auto 0 auto;\n  background: #fff;\n  border-radius: 14px;\n  box-shadow: 0 4px 24px rgba(60, 174, 163, 0.09), 0 1.5px 8px rgba(32, 99, 155, 0.05);\n  padding: 36px 32px 32px 32px;\n  min-height: 70vh;\n  transition: box-shadow 0.2s, background 0.2s;\n}\n\nh1 {\n  color: #20639b;\n  text-align: center;\n  font-size: 2.4rem;\n  margin-bottom: 24px;\n  letter-spacing: 1.5px;\n  font-weight: 800;\n}\n\nbutton {\n  font-family: inherit;\n  border: none;\n  border-radius: 7px;\n  background: #3caea3;\n  color: #fff;\n  font-size: 1.1rem;\n  font-weight: 600;\n  padding: 10px 26px;\n  margin-top: 14px;\n  cursor: pointer;\n  box-shadow: 0 1px 6px rgba(60,174,163,0.10);\n  transition: background 0.2s, box-shadow 0.2s, transform 0.1s;\n}\nbutton:hover, button:focus {\n  background: #20639b;\n  box-shadow: 0 2px 12px rgba(60,174,163,0.13);\n  transform: translateY(-2px) scale(1.03);\n}\n\n\ninput[type=\"radio\"], input[type=\"checkbox\"] {\n  accent-color: #3caea3;\n  width: 18px;\n  height: 18px;\n  margin-right: 10px;\n  vertical-align: middle;\n  transition: accent-color 0.2s;\n}\n\n.quiz-option {\n  user-select: none;\n  transition: background 0.2s, color 0.2s, border 0.2s, box-shadow 0.2s;\n}\n\n.bg-correct {\n  background: #e8fbe8 !important;\n  border-color: #7ad77a !important;\n  color: #218a5a !important;\n}\n.bg-incorrect {\n  background: #fdeaea !important;\n  border-color: #e39a9a !important;\n  color: #b22 !important;\n}\n.bg-correct-faint {\n  background: #f3fbf3 !important;\n  border-color: #b6e6b6 !important;\n  color: #218a5a !important;\n}\n\n@media (max-width: 800px) {\n  .App {\n    padding: 18px 4vw 24px 4vw;\n    min-width: 0;\n  }\n  h1 {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 500px) {\n  .App {\n    padding: 8px 2vw 16px 2vw;\n    border-radius: 0;\n  }\n  h1 {\n    font-size: 1.4rem;\n  }\n}\n"], "names": [], "sourceRoot": ""}