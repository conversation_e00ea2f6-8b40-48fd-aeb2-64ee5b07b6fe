# IndexedDB Migration Documentation

## Overview
This document describes the migration from localStorage to IndexedDB in the Snowflake Quiz application.

## Changes Made

### 1. New IndexedDB Utility Module (`src/indexedDBUtils.js`)
- Created a comprehensive IndexedDB wrapper with fallback to localStorage
- Implements two object stores:
  - `tracking`: Stores quiz performance data (correct/incorrect counts per question)
  - `session`: Stores current quiz session state for resuming
- Provides async/await API for all storage operations
- Includes automatic fallback to localStorage for browsers without IndexedDB support

### 2. Updated Quiz Component (`src/Quiz.js`)
- Replaced direct localStorage calls with IndexedDB utility functions
- Updated all storage operations to use async/await pattern
- Added proper error handling for storage operations
- Modified functions:
  - `handleAnswer()`: Now async, updates tracking data via IndexedDB
  - `handleRestart()`: Now async, clears session via IndexedDB
  - `handleExit()`: Now async, clears session via IndexedDB
- Added new useEffect to load initial tracking data from IndexedDB
- Updated session loading and saving logic to use async operations

### 3. Updated App Component (`src/App.js`)
- Replaced localStorage session functions with IndexedDB equivalents
- Updated session checking logic to use async/await
- Modified functions:
  - `getSavedSession()`: Now async, retrieves session from IndexedDB
  - `clearSavedSession()`: Now async, clears session via IndexedDB
  - `handleStart()`: Now async, clears session before starting
  - `handleClearSession()`: Now async, clears session and updates state

## Benefits of IndexedDB over localStorage

1. **Larger Storage Capacity**: IndexedDB can store much more data than localStorage (typically several GB vs 5-10MB)
2. **Better Performance**: IndexedDB operations are asynchronous and don't block the main thread
3. **Structured Data**: Native support for complex data structures without JSON serialization
4. **Transactions**: Built-in transaction support for data integrity
5. **Indexing**: Support for creating indexes for faster queries
6. **Future-Proof**: Better suited for progressive web apps and offline functionality

## Backward Compatibility

The implementation includes automatic fallback to localStorage for:
- Browsers that don't support IndexedDB
- Cases where IndexedDB initialization fails
- Maintaining the same API interface for seamless migration

## Storage Structure

### IndexedDB Database: `SnowflakeQuizDB`
- **Version**: 1
- **Object Stores**:
  - `tracking`: Stores quiz performance data
    - Key: `'snowflake-quiz-tracking-v1'`
    - Data: Object with question indices as keys and `{correct: number, incorrect: number}` as values
  - `session`: Stores current quiz session
    - Key: `'snowflake-quiz-session-v1'`
    - Data: Session object with questions, current position, user answers, etc.

## API Reference

### Storage Functions
```javascript
import { storage } from './indexedDBUtils';

// Get tracking data
const tracking = await storage.getTracking();

// Set tracking data
await storage.setTracking(trackingObject);

// Save session
await storage.saveSession(sessionObject);

// Get saved session
const session = await storage.getSavedSession();

// Clear session
await storage.clearSession();

// Check if using IndexedDB
console.log(storage.isUsingIndexedDB); // true/false
```

## Testing

To test the IndexedDB implementation:
1. Import and run the test function: `import { testIndexedDB } from './testIndexedDB';`
2. Call `testIndexedDB()` in the browser console
3. Check browser DevTools > Application > IndexedDB to see stored data

## Browser Support

- **IndexedDB**: Supported in all modern browsers (IE 10+, Chrome 24+, Firefox 16+, Safari 7+)
- **Fallback**: localStorage used for unsupported browsers
- **Progressive Enhancement**: Application works regardless of storage method

## Migration Notes

- Existing localStorage data will not be automatically migrated
- Users may need to restart their quiz sessions after the update
- The same storage keys are used for compatibility
- No changes required to the quiz logic or user interface

## Future Enhancements

With IndexedDB in place, the application can be enhanced with:
- Offline quiz functionality
- Better performance analytics
- Question caching for faster loading
- Progressive Web App (PWA) capabilities
- Advanced search and filtering of quiz history
