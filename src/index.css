body {
  margin: 0;
  font-family: 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
  background: linear-gradient(120deg, #f4f6fb 60%, #e8f7f7 100%);
  min-height: 100vh;
  color: #1b2838;
  transition: background 0.3s;
}

.App {
  max-width: 700px;
  margin: 40px auto 0 auto;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(60, 174, 163, 0.09), 0 1.5px 8px rgba(32, 99, 155, 0.05);
  padding: 36px 32px 32px 32px;
  min-height: 70vh;
  transition: box-shadow 0.2s, background 0.2s;
}

h1 {
  color: #20639b;
  text-align: center;
  font-size: 2.4rem;
  margin-bottom: 24px;
  letter-spacing: 1.5px;
  font-weight: 800;
}

button {
  font-family: inherit;
  border: none;
  border-radius: 7px;
  background: #3caea3;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 10px 26px;
  margin-top: 14px;
  cursor: pointer;
  box-shadow: 0 1px 6px rgba(60,174,163,0.10);
  transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
}
button:hover, button:focus {
  background: #20639b;
  box-shadow: 0 2px 12px rgba(60,174,163,0.13);
  transform: translateY(-2px) scale(1.03);
}


input[type="radio"], input[type="checkbox"] {
  accent-color: #3caea3;
  width: 18px;
  height: 18px;
  margin-right: 10px;
  vertical-align: middle;
  transition: accent-color 0.2s;
}

.quiz-option {
  user-select: none;
  transition: background 0.2s, color 0.2s, border 0.2s, box-shadow 0.2s;
}

.bg-correct {
  background: #e8fbe8 !important;
  border-color: #7ad77a !important;
  color: #218a5a !important;
}
.bg-incorrect {
  background: #fdeaea !important;
  border-color: #e39a9a !important;
  color: #b22 !important;
}
.bg-correct-faint {
  background: #f3fbf3 !important;
  border-color: #b6e6b6 !important;
  color: #218a5a !important;
}

@media (max-width: 800px) {
  .App {
    padding: 18px 4vw 24px 4vw;
    min-width: 0;
  }
  h1 {
    font-size: 2rem;
  }
}

@media (max-width: 500px) {
  .App {
    padding: 8px 2vw 16px 2vw;
    border-radius: 0;
  }
  h1 {
    font-size: 1.4rem;
  }
}
