import React, { useState, useEffect } from 'react';

function normalize(str) {
  return str.replace(/\s+/g, '').toUpperCase();
}

function parseOptions(options) {
  // "A. ..." => { key: "A", text: "..." }
  return options.map(opt => {
    const m = opt.match(/^([A-Z])\.\s*(.*)$/);
    return m ? { key: m[1], text: m[2] } : { key: '', text: opt };
  });
}

export default function QuizQuestion({ question, userAnswer, showExplanation, onAnswer }) {
  const options = parseOptions(question.options);
  const isMulti = question.type === 'multi';
  const [selected, setSelected] = useState(userAnswer || (isMulti ? [] : ''));
  const correct = question.answer.split(',').map(a => normalize(a));

  // Update selected state when userAnswer prop changes (for session restoration)
  useEffect(() => {
    setSelected(userAnswer || (isMulti ? [] : ''));
  }, [userAnswer, isMulti]);

  function handleChange(e, key) {
    if (isMulti) {
      if (e.target.checked) {
        setSelected([...selected, key]);
      } else {
        setSelected(selected.filter(k => k !== key));
      }
    } else {
      setSelected(key);
    }
  }

  function handleSubmit(e) {
    e.preventDefault();
    let answer = isMulti ? selected.sort() : [selected];
    let isCorrect = isMulti
      ? answer.length === correct.length && answer.every((a, i) => a === correct.sort()[i])
      : normalize(answer[0]) === correct[0];
    onAnswer(isMulti ? answer : answer[0], isCorrect);
  }

  // Highlight correct/incorrect answers
  const highlight = (opt) => {
    if (!showExplanation) return '';
    const isSelected = isMulti ? selected.includes(opt.key) : selected === opt.key;
    const isCorrectOpt = correct.includes(opt.key);
    if (isCorrectOpt && isSelected) return 'bg-correct';
    if (!isCorrectOpt && isSelected) return 'bg-incorrect';
    if (isCorrectOpt) return 'bg-correct-faint';
    return '';
  };

  return (
    <form onSubmit={handleSubmit} style={{
      background: '#f9fbfd',
      borderRadius: 12,
      boxShadow: '0 2px 12px rgba(0,0,0,0.06)',
      padding: '28px 24px',
      margin: '0 auto',
      maxWidth: 540,
      transition: 'box-shadow 0.2s',
      border: showExplanation ? '2px solid #c2e6d6' : '2px solid #e8eaf5'
    }}>
      <div style={{ fontSize: '1.15rem', fontWeight: 500, marginBottom: 20, color: '#1b2838' }}>{question.question}</div>
      <div>
        {options.map(opt => (
          <label
            key={opt.key}
            className={`quiz-option ${highlight(opt)}`}
            style={{
              display: 'flex',
              alignItems: 'center',
              margin: '10px 0',
              padding: '10px 12px',
              borderRadius: 7,
              cursor: showExplanation ? 'default' : 'pointer',
              border: '1.5px solid #e1e5f0',
              background: highlight(opt) === 'bg-correct' ? '#e8fbe8' : highlight(opt) === 'bg-incorrect' ? '#fdeaea' : highlight(opt) === 'bg-correct-faint' ? '#f3fbf3' : '#fff',
              color: highlight(opt) === 'bg-incorrect' ? '#b22' : '#223',
              opacity: showExplanation && !highlight(opt) ? 0.7 : 1,
              transition: 'background 0.2s, color 0.2s, border 0.2s',
              boxShadow: highlight(opt) ? '0 1px 6px rgba(34,139,34,0.10)' : 'none',
              fontWeight: highlight(opt) ? 600 : 400
            }}
          >
            <input
              type={isMulti ? 'checkbox' : 'radio'}
              name="quiz-option"
              value={opt.key}
              disabled={showExplanation}
              checked={isMulti ? selected.includes(opt.key) : selected === opt.key}
              onChange={e => handleChange(e, opt.key)}
              style={{ marginRight: 14, width: 18, height: 18, accentColor: highlight(opt) === 'bg-incorrect' ? '#b22' : '#2d7', cursor: showExplanation ? 'not-allowed' : 'pointer' }}
            />
            {opt.text}
          </label>
        ))}
      </div>
      {!showExplanation && (
        <button type="submit" style={{
          marginTop: 22,
          padding: '10px 32px',
          background: 'linear-gradient(90deg, #3caea3 40%, #20639b 100%)',
          color: '#fff',
          border: 'none',
          borderRadius: 7,
          fontSize: 18,
          fontWeight: 600,
          boxShadow: '0 1px 6px rgba(60,174,163,0.13)',
          cursor: 'pointer',
          transition: 'background 0.2s'
        }}>Submit</button>
      )}
      {showExplanation && (
        <div style={{
          marginTop: 16,
          fontWeight: 600,
          fontSize: 18,
          color: (userAnswer && (isMulti
            ? JSON.stringify(selected.sort()) === JSON.stringify(correct.sort())
            : normalize(selected) === correct[0])) ? '#218a5a' : '#b22',
          letterSpacing: 0.5,
          textShadow: '0 1px 0 #fff, 0 2px 6px #c2e6d6'
        }}>
          {userAnswer && ((isMulti
            ? JSON.stringify(selected.sort()) === JSON.stringify(correct.sort())
            : normalize(selected) === correct[0])
            ? 'Correct!' : 'Incorrect.')}
        </div>
      )}
    </form>
  );
}
