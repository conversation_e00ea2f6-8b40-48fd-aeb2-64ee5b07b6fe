// IndexedDB utility functions for Snowflake Quiz App
// Replaces localStorage with IndexedDB for better storage capabilities

const DB_NAME = 'SnowflakeQuizDB';
const DB_VERSION = 1;
const TRACKING_STORE = 'tracking';
const SESSION_STORE = 'session';

// Storage keys (keeping same naming for compatibility)
const STORAGE_KEY = 'snowflake-quiz-tracking-v1';
const SESSION_KEY = 'snowflake-quiz-session-v1';

let dbInstance = null;

/**
 * Initialize IndexedDB database
 * @returns {Promise<IDBDatabase>}
 */
function initDB() {
  return new Promise((resolve, reject) => {
    if (dbInstance) {
      resolve(dbInstance);
      return;
    }

    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => {
      console.error('IndexedDB error:', request.error);
      reject(request.error);
    };

    request.onsuccess = () => {
      dbInstance = request.result;
      resolve(dbInstance);
    };

    request.onupgradeneeded = (event) => {
      const db = event.target.result;

      // Create tracking store for quiz performance data
      if (!db.objectStoreNames.contains(TRACKING_STORE)) {
        const trackingStore = db.createObjectStore(TRACKING_STORE, { keyPath: 'key' });
        trackingStore.createIndex('key', 'key', { unique: true });
      }

      // Create session store for quiz session data
      if (!db.objectStoreNames.contains(SESSION_STORE)) {
        const sessionStore = db.createObjectStore(SESSION_STORE, { keyPath: 'key' });
        sessionStore.createIndex('key', 'key', { unique: true });
      }
    };
  });
}

/**
 * Generic function to get data from IndexedDB
 * @param {string} storeName - Name of the object store
 * @param {string} key - Key to retrieve
 * @returns {Promise<any>}
 */
async function getData(storeName, key) {
  try {
    const db = await initDB();
    const transaction = db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.get(key);

    return new Promise((resolve, reject) => {
      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.data : null);
      };
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error(`Error getting data from ${storeName}:`, error);
    return null;
  }
}

/**
 * Generic function to set data in IndexedDB
 * @param {string} storeName - Name of the object store
 * @param {string} key - Key to store data under
 * @param {any} data - Data to store
 * @returns {Promise<void>}
 */
async function setData(storeName, key, data) {
  try {
    const db = await initDB();
    const transaction = db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.put({ key, data });

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error(`Error setting data in ${storeName}:`, error);
    throw error;
  }
}

/**
 * Generic function to remove data from IndexedDB
 * @param {string} storeName - Name of the object store
 * @param {string} key - Key to remove
 * @returns {Promise<void>}
 */
async function removeData(storeName, key) {
  try {
    const db = await initDB();
    const transaction = db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.delete(key);

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error(`Error removing data from ${storeName}:`, error);
    throw error;
  }
}

// Tracking data functions (replaces localStorage tracking)
export async function getTracking() {
  try {
    const data = await getData(TRACKING_STORE, STORAGE_KEY);
    return data || {};
  } catch (error) {
    console.error('Error getting tracking data:', error);
    return {};
  }
}

export async function setTracking(tracking) {
  try {
    await setData(TRACKING_STORE, STORAGE_KEY, tracking);
  } catch (error) {
    console.error('Error setting tracking data:', error);
    throw error;
  }
}

// Session data functions (replaces localStorage session)
export async function saveSession(session) {
  try {
    await setData(SESSION_STORE, SESSION_KEY, session);
  } catch (error) {
    console.error('Error saving session:', error);
    throw error;
  }
}

export async function getSavedSession() {
  try {
    const data = await getData(SESSION_STORE, SESSION_KEY);
    return data;
  } catch (error) {
    console.error('Error getting saved session:', error);
    return null;
  }
}

export async function clearSession() {
  try {
    await removeData(SESSION_STORE, SESSION_KEY);
  } catch (error) {
    console.error('Error clearing session:', error);
    throw error;
  }
}

// Fallback functions for browsers that don't support IndexedDB
// These will use localStorage as a backup
const isIndexedDBSupported = () => {
  return typeof indexedDB !== 'undefined';
};

// Fallback localStorage functions (wrapped in promises for consistency)
const localStorageFallback = {
  getTracking: async () => {
    try {
      return JSON.parse(localStorage.getItem(STORAGE_KEY)) || {};
    } catch {
      return {};
    }
  },

  setTracking: async (tracking) => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(tracking));
  },

  saveSession: async (session) => {
    localStorage.setItem(SESSION_KEY, JSON.stringify(session));
  },

  getSavedSession: async () => {
    try {
      return JSON.parse(localStorage.getItem(SESSION_KEY));
    } catch {
      return null;
    }
  },

  clearSession: async () => {
    localStorage.removeItem(SESSION_KEY);
  }
};

// Export functions that automatically choose IndexedDB or localStorage fallback
export const storage = {
  getTracking: isIndexedDBSupported() ? getTracking : localStorageFallback.getTracking,
  setTracking: isIndexedDBSupported() ? setTracking : localStorageFallback.setTracking,
  saveSession: isIndexedDBSupported() ? saveSession : localStorageFallback.saveSession,
  getSavedSession: isIndexedDBSupported() ? getSavedSession : localStorageFallback.getSavedSession,
  clearSession: isIndexedDBSupported() ? clearSession : localStorageFallback.clearSession,
  isUsingIndexedDB: isIndexedDBSupported()
};

// Initialize database on module load
if (isIndexedDBSupported()) {
  initDB().catch(error => {
    console.error('Failed to initialize IndexedDB:', error);
  });
}
