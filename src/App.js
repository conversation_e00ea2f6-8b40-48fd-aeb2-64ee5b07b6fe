import React, { useState, useEffect } from 'react';
import Quiz from './Quiz';
import { storage } from './indexedDBUtils';

async function getSavedSession() {
  try {
    const session = await storage.getSavedSession();
    if (!session) return null;
    if (session.done) return null;
    // Validate that the session has the required structure
    if (!session.questions || !Array.isArray(session.questions) || session.questions.length === 0) {
      return null;
    }
    if (typeof session.current !== 'number' || session.current < 0) {
      return null;
    }
    return session;
  } catch {
    return null;
  }
}

async function clearSavedSession() {
  try {
    await storage.clearSession();
  } catch (error) {
    console.error('Error clearing saved session:', error);
  }
}

function App() {
  const [mode, setMode] = useState('quiz'); // quiz | review
  const [subset, setSubset] = useState(false);
  const [subsetCount, setSubsetCount] = useState(40);
  const [start, setStart] = useState(false);
  const [resume, setResume] = useState(false);
  const [hasSession, setHasSession] = useState(false);

  useEffect(() => {
    async function checkSession() {
      const session = await getSavedSession();
      setHasSession(!!session);
    }
    checkSession();
  }, [start, resume]);

  function handleResume() {
    setResume(true);
    setStart(true);
  }

  async function handleStart() {
    // Clear any existing session to ensure fresh start
    await clearSavedSession();
    setResume(false);
    setStart(true);
  }

  async function handleClearSession() {
    await clearSavedSession();
    setHasSession(false);
  }

  function handleQuizExit() {
    setStart(false);
    setResume(false);
  }

  return (
    <div className="App">
      <h1>Snowflake Quiz App</h1>
      {!start ? (
        <div style={{ margin: '30px 0' }}>
          <div style={{ marginBottom: 20 }}>
            <label>
              <input
                type="radio"
                name="mode"
                checked={mode === 'quiz'}
                onChange={() => setMode('quiz')}
              />{' '}
              Quiz Mode
            </label>
            <label style={{ marginLeft: 18 }}>
              <input
                type="radio"
                name="mode"
                checked={mode === 'review'}
                onChange={() => setMode('review')}
              />{' '}
              Review Incorrect Answers
            </label>
          </div>
          {mode === 'quiz' && (
            <div style={{ marginBottom: 20 }}>
              <label>
                <input
                  type="checkbox"
                  checked={subset}
                  onChange={e => setSubset(e.target.checked)}
                />{' '}
                Answer a random subset
              </label>
              {subset && (
                <input
                  type="number"
                  min={1}
                  max={50}
                  value={subsetCount}
                  style={{ width: 60, marginLeft: 8 }}
                  onChange={e => setSubsetCount(Number(e.target.value))}
                />
              )}
            </div>
          )}
          <button onClick={handleStart} style={{ fontSize: 18, padding: '8px 22px', marginRight: hasSession ? 16 : 0 }}>Start Fresh</button>
          {hasSession && (
            <>
              <button onClick={handleResume} style={{ fontSize: 18, padding: '8px 22px', background: '#20639b', marginRight: 16 }}>Resume Session</button>
              <button onClick={handleClearSession} style={{ fontSize: 14, padding: '6px 12px', background: '#dc3545', color: 'white' }}>Clear Session</button>
            </>
          )}
        </div>
      ) : (
        <>
          <Quiz
            mode={mode}
            subset={mode === 'quiz' && subset}
            subsetCount={subsetCount}
            resume={resume}
          />
          <div style={{ marginTop: 40, display: 'flex', gap: 16, justifyContent: 'center' }}>
            <button onClick={handleQuizExit}>Back to Menu</button>
            <button style={{ background: '#b22222' }} onClick={() => window.location.reload()}>Exit</button>
          </div>
        </>
      )}
    </div>
  );
}

export default App;
