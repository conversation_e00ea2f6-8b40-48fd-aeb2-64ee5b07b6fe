import React, { useState, useEffect, useRef } from 'react';
import { parseQuestions } from './parseQuestions';
import { loadQuestionsText } from './loadQuestions';
import QuizQuestion from './QuizQuestion';

const STORAGE_KEY = 'snowflake-quiz-tracking-v1';
const SESSION_KEY = 'snowflake-quiz-session-v1';

function getTracking() {
  try {
    return JSON.parse(localStorage.getItem(STORAGE_KEY)) || {};
  } catch {
    return {};
  }
}
function setTracking(tracking) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(tracking));
}

function saveSession(session) {
  localStorage.setItem(SESSION_KEY, JSON.stringify(session));
}
function clearSession() {
  localStorage.removeItem(SESSION_KEY);
}
function getSavedSession() {
  try {
    return JSON.parse(localStorage.getItem(SESSION_KEY));
  } catch {
    return null;
  }
}

export default function Quiz({ mode = 'quiz', subset = false, subsetCount = 10, resume = false, onExit }) {
  const [questions, setQuestions] = useState([]);
  const [current, setCurrent] = useState(0);
  const [userAnswers, setUserAnswers] = useState([]);
  const [showExplanation, setShowExplanation] = useState(false);
  const [done, setDone] = useState(false);
  const [tracking, setTrackingState] = useState(getTracking());
  const [loading, setLoading] = useState(true);
  const sessionLoaded = useRef(false);
  const [reviewMode, setReviewMode] = useState(false);
  const [reviewCurrent, setReviewCurrent] = useState(0);
  const [progressCollapsed, setProgressCollapsed] = useState(false);

  // Load questions and session state
  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    
    // Check for session restoration first
    if (resume && !sessionLoaded.current) {
      const session = getSavedSession();
      if (session && session.questions && Array.isArray(session.questions) && session.questions.length > 0) {
        console.log('Restoring session with', session.questions.length, 'questions at position', session.current);
        
        // Ensure userAnswers is properly structured
        const restoredAnswers = session.userAnswers || Array(session.questions.length).fill(null);
        console.log('Sample restored answers:', restoredAnswers.slice(0, 3));
        console.log('Current answer:', restoredAnswers[session.current]);
        setQuestions(session.questions);
        setUserAnswers(restoredAnswers);
        setCurrent(session.current || 0);
        setDone(session.done || false);
        setShowExplanation(session.showExplanation || false);
        sessionLoaded.current = true;
        setLoading(false);
        return;
      } else {
        console.log('No valid session found to restore');
      }
    }
    
    // Load fresh questions
    loadQuestionsText().then(txt => {
      if (!isMounted) return;
      
      let qs = parseQuestions(txt);
      if (mode === 'review') {
        qs = qs.filter((q, idx) => {
          const t = tracking[idx] || { correct: 0, incorrect: 0 };
          return t.incorrect > t.correct;
        });
      } else if (subset && qs.length > subsetCount) {
        qs = qs.sort(() => 0.5 - Math.random()).slice(0, subsetCount);
      }
      
      setQuestions(qs);
      setUserAnswers(Array(qs.length).fill(null));
      setCurrent(0);
      setDone(false);
      setShowExplanation(false);
      setLoading(false);
    }).catch(error => {
      console.error('Error loading questions:', error);
      setLoading(false);
    });
    
    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line
  }, [mode, subset, subsetCount, resume]);

  // Save session after each answer/progress (except when done)
  useEffect(() => {
    if (!loading && questions.length && !done) {
      try {
        const sessionData = {
          questions,
          current,
          userAnswers,
          showExplanation,
          done: false,
          timestamp: Date.now()
        };
        console.log('Saving session:', sessionData);
        saveSession(sessionData);
      } catch (error) {
        console.error('Error saving session:', error);
      }
    }
    if (done) {
      console.log('Quiz completed, clearing session');
      clearSession();
    }
    // eslint-disable-next-line
  }, [questions, current, userAnswers, showExplanation, done, loading]);

  function handleAnswer(idx, answer, isCorrect) {
    const updated = [...userAnswers];
    updated[idx] = { answer, isCorrect };
    setUserAnswers(updated);
    setShowExplanation(true);
    // Update local storage tracking
    const qidx = questions[idx]._originalIndex ?? idx;
    const t = { ...(tracking[qidx] || { correct: 0, incorrect: 0 }) };
    if (isCorrect) t.correct++;
    else t.incorrect++;
    const newTracking = { ...tracking, [qidx]: t };
    setTracking(newTracking);
    setTrackingState(newTracking);
  }

  function handleNext() {
    setShowExplanation(false);
    if (current + 1 < questions.length) setCurrent(current + 1);
    else setDone(true);
  }

  function handlePrevious() {
    setShowExplanation(false);
    if (current > 0) setCurrent(current - 1);
  }

  function handleGoToQuestion(index) {
    setShowExplanation(false);
    setCurrent(index);
  }

  function handleRestart() {
    setCurrent(0);
    setDone(false);
    setUserAnswers(Array(questions.length).fill(null));
    setShowExplanation(false);
    clearSession();
    sessionLoaded.current = false;
  }

  function handleExit() {
    clearSession();
    if (onExit) onExit();
  }

  if (loading) return <div>Loading questions...</div>;
  if (!questions.length) return <div>No questions available.</div>;
  if (done) {
    const incorrectIndexes = userAnswers
      .map((a, i) => (!a?.isCorrect ? i : null))
      .filter(i => i !== null);
    const incorrectQuestions = incorrectIndexes.map(i => questions[i]);
    const incorrectUserAnswers = incorrectIndexes.map(i => userAnswers[i]);

    if (reviewMode) {
      // Show all incorrect questions on one page
      return (
        <div>
          <h2>Review Incorrect Answers</h2>
          {incorrectQuestions.length === 0 && (
            <div style={{ margin: '20px 0' }}>No incorrect answers to review.</div>
          )}
          <div style={{ display: 'flex', flexDirection: 'column', gap: 32 }}>
            {incorrectQuestions.map((q, idx) => (
              <div key={idx} style={{ border: '1px solid #e0e0e0', borderRadius: 8, padding: 18, background: '#fcfcfc', boxShadow: '0 1px 6px #e0f6f6' }}>
                <div style={{ fontWeight: 600, marginBottom: 8 }}>Question {idx + 1}:</div>
                <QuizQuestion
                  question={q}
                  userAnswer={incorrectUserAnswers[idx]?.answer}
                  showExplanation={true}
                  onAnswer={() => {}}
                />
                <div style={{ margin: '14px 0 0 0', color: '#286', background: '#e8fbe8', borderRadius: 6, padding: 10 }}>
                  <b>Explanation:</b> {q.explanation || 'No explanation provided.'}
                </div>
              </div>
            ))}
          </div>
          <button style={{ marginTop: 32 }} onClick={() => setReviewMode(false)}>Back to Results</button>
        </div>
      );
    }

    return (
      <div>
        <h2>Quiz Complete!</h2>
        <p>You answered {userAnswers.filter(a => a?.isCorrect).length} out of {questions.length} correctly.</p>
        {incorrectIndexes.length > 0 && (
          <button style={{ marginRight: 18 }} onClick={() => setReviewMode(true)}>Review Incorrect Answers</button>
        )}
        <button onClick={handleRestart}>Restart</button>
        <button style={{ marginLeft: 18 }} onClick={handleExit}>Exit</button>
      </div>
    );
  }

  const q = questions[current];
  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <b>Question {current + 1} of {questions.length}</b>
      </div>
      
      {/* Collapsible Question Progress Indicator */}
      <div style={{ marginBottom: 20, padding: 12, backgroundColor: '#f8f9fa', borderRadius: 6 }}>
        <div 
          style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            cursor: 'pointer',
            marginBottom: progressCollapsed ? 0 : 8
          }}
          onClick={() => setProgressCollapsed(!progressCollapsed)}
        >
          <div style={{ fontSize: 14, color: '#666' }}>
            Progress: {userAnswers.filter(a => a !== null).length} / {questions.length}
            {userAnswers.filter(a => a !== null).length > 0 && (
              <span style={{ marginLeft: 8 }}>
                (✓ {userAnswers.filter(a => a?.isCorrect).length} correct, 
                ✗ {userAnswers.filter(a => a && !a.isCorrect).length} incorrect)
              </span>
            )}
          </div>
          <span style={{ fontSize: 16, color: '#666' }}>
            {progressCollapsed ? '▼' : '▲'}
          </span>
        </div>
        
        {!progressCollapsed && (
          <>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
              {questions.map((_, index) => {
                const answer = userAnswers[index];
                const isAnswered = answer !== null;
                const isCurrent = index === current;
                const isCorrect = answer?.isCorrect;
                
                let backgroundColor, symbol;
                if (isCurrent) {
                  backgroundColor = '#007bff';
                  symbol = '';
                } else if (isAnswered) {
                  backgroundColor = isCorrect ? '#28a745' : '#dc3545';
                  symbol = isCorrect ? '✓' : '✗';
                } else {
                  backgroundColor = '#fff';
                  symbol = '';
                }
                
                return (
                  <button
                    key={index}
                    onClick={() => handleGoToQuestion(index)}
                    style={{
                      width: 36,
                      height: 36,
                      border: isCurrent ? '2px solid #007bff' : '1px solid #ccc',
                      borderRadius: 4,
                      backgroundColor,
                      color: isAnswered || isCurrent ? 'white' : '#333',
                      fontSize: 10,
                      cursor: 'pointer',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative'
                    }}
                    title={`Question ${index + 1}${isAnswered ? (isCorrect ? ' (Correct)' : ' (Incorrect)') : ' (Unanswered)'}`}
                  >
                    <div style={{ fontSize: 10 }}>{index + 1}</div>
                    {symbol && <div style={{ fontSize: 8, lineHeight: 1 }}>{symbol}</div>}
                  </button>
                );
              })}
            </div>
            <div style={{ fontSize: 12, marginTop: 8, color: '#666', display: 'flex', gap: 16 }}>
              <span>🟢 Correct: {userAnswers.filter(a => a?.isCorrect).length}</span>
              <span>🔴 Incorrect: {userAnswers.filter(a => a && !a.isCorrect).length}</span>
              <span>⚪ Unanswered: {userAnswers.filter(a => a === null).length}</span>
            </div>
          </>
        )}
      </div>
      <QuizQuestion
        key={current}
        question={q}
        userAnswer={userAnswers[current]?.answer}
        showExplanation={showExplanation}
        onAnswer={(answer, isCorrect) => handleAnswer(current, answer, isCorrect)}
      />
      {/* Debug info */}
      <div style={{ fontSize: '12px', color: '#666', marginTop: '10px', padding: '10px', background: '#f5f5f5' }}>
        Debug: current={current}, userAnswers[{current}]={JSON.stringify(userAnswers[current])}, 
        userAnswer prop={JSON.stringify(userAnswers[current]?.answer)}
      </div>
      {showExplanation && (
        <div style={{ margin: '20px 0', color: '#286', background: '#e8fbe8', borderRadius: 6, padding: 12 }}>
          <b>Explanation:</b> {q.explanation || 'No explanation provided.'}
        </div>
      )}
      <div style={{ marginTop: 20, display: 'flex', gap: 12, alignItems: 'center' }}>
        <button 
          onClick={handlePrevious} 
          disabled={current === 0}
          style={{ 
            padding: '8px 16px',
            backgroundColor: current === 0 ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: 4,
            cursor: current === 0 ? 'not-allowed' : 'pointer'
          }}
        >
          ← Previous
        </button>
        
        {showExplanation && (
          <button 
            onClick={handleNext} 
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: 4,
              cursor: 'pointer'
            }}
          >
            {current + 1 === questions.length ? 'Finish' : 'Next →'}
          </button>
        )}
        
        {!showExplanation && current + 1 < questions.length && (
          <button 
            onClick={handleNext} 
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: 4,
              cursor: 'pointer'
            }}
          >
            Skip →
          </button>
        )}
        
        {!showExplanation && current + 1 === questions.length && (
          <button 
            onClick={() => setDone(true)} 
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: 4,
              cursor: 'pointer'
            }}
          >
            Finish Quiz
          </button>
        )}
      </div>
    </div>
  );
}
