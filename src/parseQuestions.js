// Utility to parse the snowflake questions.txt file into a JS array of questions

/**
 * @param {string} text
 * @returns {Array} Array of question objects
 */
export function parseQuestions(text) {
  // Split by lines, iterate, and build up questions
  const lines = text.split(/\r?\n/);
  const questions = [];
  let current = null;

  function pushCurrent() {
    if (current && current.question && current.options.length > 0 && current.answer) {
      questions.push({ ...current });
    }
    current = null;
  }

  let i = 0;
  while (i < lines.length) {
    let line = lines[i].trim();
    if (!line) { i++; continue; } // skip blank lines
    if (/^Question:/.test(line)) {
      pushCurrent();
      current = {
        question: '',
        options: [],
        answer: '',
        explanation: '',
        type: 'single',
      };
      // Remove 'Question:' and any number
      let questionText = line.replace(/^Question:\s*\d*\s*/, '').trim();
      i++;
      // Gather question text from subsequent lines until we hit an option, answer, or explanation
      while (i < lines.length) {
        let next = lines[i].trim();
        if (!next) { i++; continue; }
        if (/^[A-Z]\. /.test(next) || /^Answer:/.test(next) || /^Explanation:/.test(next) || /^Question:/.test(next)) break;
        questionText += (questionText ? ' ' : '') + next;
        i++;
      }
      current.question = questionText;
      continue;
    }
    if (current && /^[A-Z]\. /.test(line)) {
      current.options.push(line);
      i++;
      continue;
    }
    if (current && /^Answer:/.test(line)) {
      current.answer = line.replace(/^Answer:\s*/, '').trim();
      if (current.answer.includes(',')) current.type = 'multi';
      i++;
      continue;
    }
    if (current && /^Explanation:/.test(line)) {
      current.explanation = line.replace(/^Explanation:\s*/, '').trim();
      i++;
      // Gather multi-line explanation
      while (i < lines.length) {
        let expl = lines[i].trim();
        if (!expl || /^Question:/.test(expl) || /^[A-Z]\. /.test(expl) || /^Answer:/.test(expl)) break;
        current.explanation += ' ' + expl;
        i++;
      }
      continue;
    }
    i++;
  }
  pushCurrent();
  return questions;
}
